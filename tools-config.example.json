{"description": "Example configuration for the Function Tools system", "version": "1.0.0", "discovery": {"custom-cli-tools": {"description": "Discover tools from a custom CLI tool manager", "toolDiscoveryCommand": "my-tool-manager list --format=json", "toolCallCommand": "my-tool-manager execute <toolName>", "workingDirectory": "/path/to/your/tools", "timeout": 30000, "enabled": false, "notes": "Replace with your actual tool manager commands"}, "python-scripts": {"description": "Discover Python script tools", "toolDiscoveryCommand": "python scripts/discover_tools.py", "toolCallCommand": "python scripts/execute_tool.py <toolName>", "workingDirectory": "/path/to/python/tools", "timeout": 45000, "enabled": false, "notes": "Requires Python scripts that implement tool discovery protocol"}, "node-tools": {"description": "Discover Node.js based tools", "toolDiscoveryCommand": "node tools/discover.js", "toolCallCommand": "node tools/execute.js <toolName>", "workingDirectory": "/path/to/node/tools", "timeout": 30000, "enabled": false, "notes": "Requires Node.js scripts that return JSON tool definitions"}}, "mcpServers": {"filesystem-server": {"description": "Local filesystem access via MCP", "type": "stdio", "command": "mcp-server-filesystem", "args": ["--root", "/path/to/allowed/directory"], "workingDirectory": "/path/to/mcp/servers", "enabled": false, "notes": "Requires mcp-server-filesystem to be installed"}, "database-server": {"description": "Database access via MCP", "type": "stdio", "command": "mcp-server-database", "args": ["--connection", "postgresql://user:pass@localhost/db"], "workingDirectory": "/path/to/mcp/servers", "enabled": false, "notes": "Requires mcp-server-database and proper database credentials"}, "api-gateway": {"description": "Remote API access via HTTP MCP", "type": "http", "url": "https://api.example.com/mcp", "headers": {"Authorization": "Bearer your-api-token-here", "Content-Type": "application/json"}, "connectionTimeout": 10000, "requestTimeout": 30000, "enabled": false, "notes": "Replace with your actual API endpoint and credentials"}, "sse-service": {"description": "Real-time service via SSE MCP", "type": "sse", "url": "https://realtime.example.com/mcp", "headers": {"Authorization": "Bearer your-sse-token-here"}, "connectionTimeout": 15000, "requestTimeout": 60000, "enabled": false, "notes": "For services that provide real-time data via Server-Sent Events"}, "local-dev-server": {"description": "Local development MCP server", "type": "http", "url": "http://localhost:8080/mcp", "headers": {"X-Dev-Mode": "true"}, "enabled": false, "notes": "For local development and testing"}}, "mcpAllowlist": ["filesystem-server", "database-server:read-only-query", "api-gateway:safe-operations", "local-dev-server"], "webSearch": {"description": "Web search API configuration", "enableDuckDuckGo": true, "enableSerpAPI": true, "enableBing": true, "notes": {"duckduckgo": "Always available, no API key required", "serpapi": "Requires SERPAPI_KEY environment variable", "bing": "Requires BING_SEARCH_API_KEY environment variable"}}, "security": {"description": "Security and safety configuration", "requireConfirmationForDangerous": true, "allowShellCommands": true, "maxFileSize": "10MB", "maxFiles": 100, "timeout": 30000, "notes": "These settings control tool safety and resource limits"}, "environment": {"description": "Environment variables to set", "variables": {"TOOL_DISCOVERY_ENABLED": "true", "MCP_FILESYSTEM_ENABLED": "true", "MCP_ALLOWLIST": "filesystem-server,api-gateway:safe-operations", "SERPAPI_KEY": "your_serpapi_key_here", "BING_SEARCH_API_KEY": "your_bing_api_key_here"}, "notes": "Set these environment variables to enable various features"}, "examples": {"toolDiscoveryScript": {"description": "Example Python script for tool discovery", "filename": "discover_tools.py", "content": "#!/usr/bin/env python3\nimport json\n\ntools = [\n    {\n        'name': 'example_tool',\n        'description': 'An example tool',\n        'parameters': {\n            'type': 'object',\n            'properties': {\n                'input': {'type': 'string', 'description': 'Input text'}\n            },\n            'required': ['input']\n        }\n    }\n]\n\nprint(json.dumps(tools))"}, "mcpServerSetup": {"description": "Example MCP server setup", "steps": ["1. Install MCP server: npm install -g @modelcontextprotocol/server-filesystem", "2. Configure server in mcpServers section above", "3. Add server to mcpAllowlist", "4. Set MCP_FILESYSTEM_ENABLED=true in environment", "5. Run tool initialization to discover MCP tools"]}}, "troubleshooting": {"common_issues": {"no_search_results": "Set SERPAPI_KEY or BING_SEARCH_API_KEY environment variables for real web search", "mcp_connection_failed": "Check that MCP server is installed and command path is correct", "tool_discovery_empty": "Verify toolDiscoveryCommand returns valid JSON array of tool definitions", "permission_denied": "Add tools/servers to allowlist or check file permissions"}}}
import { spawn } from 'child_process';
import { EventEmitter } from 'events';
import axios from 'axios';
import { MCPTransportType, MCPDefaults } from '../base/tool-types.js';
import logger from '../../utils/logger.js';

/**
 * Base MCP Transport class
 */
export class MCPTransport extends EventEmitter {
  constructor(config) {
    super();
    this.config = {
      ...MCPDefaults,
      ...config
    };
    this.connected = false;
    this.requestId = 0;
    this.pendingRequests = new Map();
  }

  /**
   * Connect to MCP server
   */
  async connect() {
    throw new Error('connect() must be implemented by subclass');
  }

  /**
   * Disconnect from MCP server
   */
  async disconnect() {
    throw new Error('disconnect() must be implemented by subclass');
  }

  /**
   * Send request to MCP server
   */
  async sendRequest(method, params = {}) {
    throw new Error('sendRequest() must be implemented by subclass');
  }

  /**
   * Generate unique request ID
   */
  generateRequestId() {
    return ++this.requestId;
  }

  /**
   * Handle response from MCP server
   */
  handleResponse(response) {
    const { id, result, error } = response;
    
    if (this.pendingRequests.has(id)) {
      const { resolve, reject } = this.pendingRequests.get(id);
      this.pendingRequests.delete(id);
      
      if (error) {
        reject(new Error(error.message || 'MCP request failed'));
      } else {
        resolve(result);
      }
    }
  }

  /**
   * Create request promise
   */
  createRequestPromise(id) {
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject });
      
      // Set timeout
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error('MCP request timeout'));
        }
      }, this.config.requestTimeout);
    });
  }
}

/**
 * Stdio MCP Transport for command-line MCP servers
 */
export class StdioMCPTransport extends MCPTransport {
  constructor(config) {
    super(config);
    this.process = null;
    this.buffer = '';
  }

  /**
   * Connect to stdio MCP server
   */
  async connect() {
    if (this.connected) {
      return;
    }

    try {
      logger.debug(`Connecting to stdio MCP server: ${this.config.command}`);
      
      const args = this.config.args || [];
      this.process = spawn(this.config.command, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: this.config.workingDirectory || process.cwd(),
        env: { ...process.env, ...this.config.env }
      });

      // Handle stdout (responses)
      this.process.stdout.on('data', (data) => {
        this.handleStdoutData(data);
      });

      // Handle stderr (errors/logs)
      this.process.stderr.on('data', (data) => {
        logger.debug(`MCP server stderr: ${data.toString()}`);
      });

      // Handle process exit
      this.process.on('exit', (code) => {
        logger.debug(`MCP server exited with code: ${code}`);
        this.connected = false;
        this.emit('disconnect');
      });

      // Handle process errors
      this.process.on('error', (error) => {
        logger.error('MCP server process error:', error.message);
        this.connected = false;
        this.emit('error', error);
      });

      // Wait for process to start
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('MCP server connection timeout'));
        }, this.config.connectionTimeout);

        this.process.on('spawn', () => {
          clearTimeout(timeout);
          this.connected = true;
          resolve();
        });

        this.process.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      logger.debug('Connected to stdio MCP server');
      this.emit('connect');

    } catch (error) {
      logger.error('Failed to connect to stdio MCP server:', error.message);
      throw error;
    }
  }

  /**
   * Disconnect from stdio MCP server
   */
  async disconnect() {
    if (!this.connected || !this.process) {
      return;
    }

    try {
      this.process.kill('SIGTERM');
      
      // Wait for process to exit
      await new Promise((resolve) => {
        this.process.on('exit', resolve);
        setTimeout(resolve, 5000); // Force timeout after 5 seconds
      });

      this.connected = false;
      this.process = null;
      
      logger.debug('Disconnected from stdio MCP server');
      this.emit('disconnect');

    } catch (error) {
      logger.error('Error disconnecting from stdio MCP server:', error.message);
    }
  }

  /**
   * Send request to stdio MCP server
   */
  async sendRequest(method, params = {}) {
    if (!this.connected || !this.process) {
      throw new Error('Not connected to MCP server');
    }

    const id = this.generateRequestId();
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      params
    };

    const requestJson = JSON.stringify(request) + '\n';
    
    logger.debug(`Sending MCP request: ${method}`);
    
    const promise = this.createRequestPromise(id);
    this.process.stdin.write(requestJson);
    
    return promise;
  }

  /**
   * Handle stdout data from MCP server
   */
  handleStdoutData(data) {
    this.buffer += data.toString();
    
    // Process complete JSON-RPC messages
    const lines = this.buffer.split('\n');
    this.buffer = lines.pop() || ''; // Keep incomplete line in buffer
    
    for (const line of lines) {
      if (line.trim()) {
        try {
          const response = JSON.parse(line);
          this.handleResponse(response);
        } catch (error) {
          logger.error('Failed to parse MCP response:', error.message);
        }
      }
    }
  }
}

/**
 * SSE MCP Transport for Server-Sent Events over HTTP
 */
export class SSEMCPTransport extends MCPTransport {
  constructor(config) {
    super(config);
    this.eventSource = null;
    this.baseUrl = config.url;
  }

  /**
   * Connect to SSE MCP server
   */
  async connect() {
    if (this.connected) {
      return;
    }

    try {
      logger.debug(`Connecting to SSE MCP server: ${this.baseUrl}`);
      
      // Note: In a real implementation, you would use the EventSource API
      // This is a simplified version for demonstration
      this.connected = true;
      
      logger.debug('Connected to SSE MCP server');
      this.emit('connect');

    } catch (error) {
      logger.error('Failed to connect to SSE MCP server:', error.message);
      throw error;
    }
  }

  /**
   * Disconnect from SSE MCP server
   */
  async disconnect() {
    if (!this.connected) {
      return;
    }

    try {
      if (this.eventSource) {
        this.eventSource.close();
        this.eventSource = null;
      }

      this.connected = false;
      
      logger.debug('Disconnected from SSE MCP server');
      this.emit('disconnect');

    } catch (error) {
      logger.error('Error disconnecting from SSE MCP server:', error.message);
    }
  }

  /**
   * Send request to SSE MCP server
   */
  async sendRequest(method, params = {}) {
    if (!this.connected) {
      throw new Error('Not connected to MCP server');
    }

    const id = this.generateRequestId();
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      params
    };

    logger.debug(`Sending SSE MCP request: ${method}`);

    try {
      const response = await axios.post(`${this.baseUrl}/rpc`, request, {
        timeout: this.config.requestTimeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      return response.data.result;

    } catch (error) {
      logger.error('SSE MCP request failed:', error.message);
      throw error;
    }
  }
}

/**
 * HTTP MCP Transport for streamable HTTP
 */
export class HTTPMCPTransport extends MCPTransport {
  constructor(config) {
    super(config);
    this.baseUrl = config.url;
    this.headers = config.headers || {};
  }

  /**
   * Connect to HTTP MCP server
   */
  async connect() {
    if (this.connected) {
      return;
    }

    try {
      logger.debug(`Connecting to HTTP MCP server: ${this.baseUrl}`);
      
      // Test connection with a ping or capabilities request
      await this.sendRequest('ping');
      
      this.connected = true;
      
      logger.debug('Connected to HTTP MCP server');
      this.emit('connect');

    } catch (error) {
      logger.error('Failed to connect to HTTP MCP server:', error.message);
      throw error;
    }
  }

  /**
   * Disconnect from HTTP MCP server
   */
  async disconnect() {
    if (!this.connected) {
      return;
    }

    this.connected = false;
    
    logger.debug('Disconnected from HTTP MCP server');
    this.emit('disconnect');
  }

  /**
   * Send request to HTTP MCP server
   */
  async sendRequest(method, params = {}) {
    if (!this.connected) {
      throw new Error('Not connected to MCP server');
    }

    const id = this.generateRequestId();
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      params
    };

    logger.debug(`Sending HTTP MCP request: ${method}`);

    try {
      const response = await axios.post(`${this.baseUrl}/rpc`, request, {
        timeout: this.config.requestTimeout,
        headers: {
          'Content-Type': 'application/json',
          ...this.headers
        }
      });

      if (response.data.error) {
        throw new Error(response.data.error.message || 'MCP request failed');
      }

      return response.data.result;

    } catch (error) {
      logger.error('HTTP MCP request failed:', error.message);
      throw error;
    }
  }
}

/**
 * MCP Transport Factory
 */
export class MCPTransportFactory {
  /**
   * Create transport based on configuration
   */
  static createTransport(config) {
    switch (config.type) {
      case MCPTransportType.STDIO:
        return new StdioMCPTransport(config);
      
      case MCPTransportType.SSE:
        return new SSEMCPTransport(config);
      
      case MCPTransportType.HTTP:
        return new HTTPMCPTransport(config);
      
      default:
        throw new Error(`Unsupported MCP transport type: ${config.type}`);
    }
  }
}

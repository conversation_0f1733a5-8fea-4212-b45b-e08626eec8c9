#!/usr/bin/env node

/**
 * Setup script for the Function Tools system
 * 
 * This script helps initialize and configure the comprehensive tool system.
 * Run with: node setup-tools.js
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs-extra';
import { 
  initializeTools, 
  getToolStats,
  configureToolDiscovery,
  configureMCPServers,
  addToMCPAllowlist
} from './src/tools/index.js';
import toolConfig from './src/tools/config/tool-config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Main setup function
 */
async function main() {
  console.log('🔧 Function Tools System Setup');
  console.log('================================\n');

  try {
    // Step 1: Check environment
    console.log('📋 Checking environment...');
    const status = toolConfig.getToolConfigStatus();
    
    console.log(`   Web Search APIs:`);
    console.log(`   - DuckDuckGo: ✅ Available (no API key required)`);
    console.log(`   - SerpAPI: ${status.webSearch.serpAPI ? '✅' : '❌'} ${status.webSearch.serpAPI ? 'Configured' : 'Missing SERPAPI_KEY'}`);
    console.log(`   - Bing: ${status.webSearch.bing ? '✅' : '❌'} ${status.webSearch.bing ? 'Configured' : 'Missing BING_SEARCH_API_KEY'}`);
    
    console.log(`   Tool Discovery: ${status.environment.hasToolDiscoveryEnabled ? '✅' : '❌'} ${status.environment.hasToolDiscoveryEnabled ? 'Enabled' : 'Set TOOL_DISCOVERY_ENABLED=true'}`);
    console.log(`   MCP Filesystem: ${status.environment.hasMCPFilesystemEnabled ? '✅' : '❌'} ${status.environment.hasMCPFilesystemEnabled ? 'Enabled' : 'Set MCP_FILESYSTEM_ENABLED=true'}`);
    console.log(`   MCP Allowlist: ${status.environment.hasMCPAllowlist ? '✅' : '❌'} ${status.environment.hasMCPAllowlist ? 'Configured' : 'Set MCP_ALLOWLIST env var'}`);
    console.log();

    // Step 2: Load and validate configuration
    console.log('⚙️  Loading configuration...');
    const config = toolConfig.loadToolConfig();
    const validation = toolConfig.validateToolConfig(config);
    
    if (!validation.isValid) {
      console.log('⚠️  Configuration validation warnings:');
      validation.errors.forEach(error => console.log(`   - ${error}`));
      console.log();
    } else {
      console.log('   ✅ Configuration is valid');
    }

    // Step 3: Apply configuration
    console.log('🔧 Applying configuration...');
    const applied = toolConfig.applyToolConfig(config);
    if (applied) {
      console.log('   ✅ Configuration applied successfully');
    } else {
      console.log('   ❌ Failed to apply configuration');
      return;
    }

    // Step 4: Initialize tools
    console.log('🚀 Initializing tools...');
    await initializeTools();
    console.log('   ✅ Core tools initialized');

    // Step 5: Get statistics
    console.log('📊 Tool system statistics:');
    const stats = getToolStats();
    console.log(`   - Total tools: ${stats.totalTools}`);
    console.log(`   - Categories: ${Object.keys(stats.categories).join(', ')}`);
    
    for (const [category, count] of Object.entries(stats.categories)) {
      console.log(`     * ${category}: ${count} tools`);
    }
    console.log();

    // Step 6: Check for example config
    const exampleConfigPath = join(__dirname, 'tools-config.example.json');
    if (!fs.existsSync(exampleConfigPath)) {
      console.log('📝 Creating example configuration file...');
      const exampleConfig = toolConfig.generateExampleConfig();
      await fs.writeJSON(exampleConfigPath, exampleConfig, { spaces: 2 });
      console.log(`   ✅ Created ${exampleConfigPath}`);
    }

    // Step 7: Success message
    console.log('🎉 Setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Review tools-config.example.json for configuration options');
    console.log('2. Set environment variables for web search APIs:');
    console.log('   export SERPAPI_KEY="your_key_here"');
    console.log('   export BING_SEARCH_API_KEY="your_key_here"');
    console.log('3. Enable tool discovery and MCP servers as needed:');
    console.log('   export TOOL_DISCOVERY_ENABLED=true');
    console.log('   export MCP_FILESYSTEM_ENABLED=true');
    console.log('4. Configure MCP allowlist:');
    console.log('   export MCP_ALLOWLIST="server1,server2:tool1"');
    console.log('\nFor more information, see src/tools/README.md');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    console.error('\nFor troubleshooting, check:');
    console.error('- Node.js version (requires Node 16+)');
    console.error('- File permissions in the project directory');
    console.error('- Network connectivity for web search APIs');
    console.error('\nFull error details:');
    console.error(error.stack);
    process.exit(1);
  }
}

/**
 * Interactive setup mode
 */
async function interactiveSetup() {
  console.log('🔧 Interactive Function Tools Setup');
  console.log('===================================\n');

  try {
    // Run the setup wizard
    await toolConfig.setupWizard();
    
    console.log('\n🎉 Interactive setup completed!');
    console.log('The tool system is now configured and ready to use.');
    
  } catch (error) {
    console.error('❌ Interactive setup failed:', error.message);
    process.exit(1);
  }
}

/**
 * Show help information
 */
function showHelp() {
  console.log('Function Tools Setup Script');
  console.log('===========================\n');
  console.log('Usage: node setup-tools.js [options]\n');
  console.log('Options:');
  console.log('  --interactive, -i    Run interactive setup wizard');
  console.log('  --help, -h          Show this help message');
  console.log('  --status, -s        Show current configuration status');
  console.log('  --example, -e       Generate example configuration file');
  console.log('\nExamples:');
  console.log('  node setup-tools.js              # Run basic setup');
  console.log('  node setup-tools.js --interactive # Run interactive setup');
  console.log('  node setup-tools.js --status     # Check current status');
  console.log('  node setup-tools.js --example    # Generate example config');
}

/**
 * Show current status
 */
async function showStatus() {
  console.log('📊 Function Tools Status');
  console.log('========================\n');

  try {
    const status = toolConfig.getToolConfigStatus();
    
    console.log('Environment Configuration:');
    console.log(`- Tool Discovery: ${status.environment.hasToolDiscoveryEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`- MCP Filesystem: ${status.environment.hasMCPFilesystemEnabled ? 'Enabled' : 'Disabled'}`);
    console.log(`- MCP Allowlist: ${status.environment.hasMCPAllowlist ? 'Configured' : 'Not configured'}`);
    console.log();
    
    console.log('Web Search APIs:');
    console.log(`- DuckDuckGo: Available (no API key required)`);
    console.log(`- SerpAPI: ${status.webSearch.serpAPI ? 'Configured' : 'Not configured (set SERPAPI_KEY)'}`);
    console.log(`- Bing: ${status.webSearch.bing ? 'Configured' : 'Not configured (set BING_SEARCH_API_KEY)'}`);
    console.log();

    // Try to get tool stats if tools are initialized
    try {
      await initializeTools();
      const stats = getToolStats();
      console.log('Tool Registry:');
      console.log(`- Total tools: ${stats.totalTools}`);
      for (const [category, count] of Object.entries(stats.categories)) {
        console.log(`- ${category}: ${count} tools`);
      }
    } catch (error) {
      console.log('Tool Registry: Not initialized (run setup first)');
    }

  } catch (error) {
    console.error('❌ Failed to get status:', error.message);
    process.exit(1);
  }
}

/**
 * Generate example configuration
 */
async function generateExample() {
  console.log('📝 Generating Example Configuration');
  console.log('==================================\n');

  try {
    const exampleConfig = toolConfig.generateExampleConfig();
    const outputPath = join(__dirname, 'tools-config.example.json');
    
    await fs.writeJSON(outputPath, exampleConfig, { spaces: 2 });
    
    console.log(`✅ Example configuration written to: ${outputPath}`);
    console.log('\nYou can use this as a starting point for your tool configuration.');
    console.log('Copy it to tools-config.json and modify as needed.');

  } catch (error) {
    console.error('❌ Failed to generate example:', error.message);
    process.exit(1);
  }
}

// Parse command line arguments and run appropriate function
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  showHelp();
} else if (args.includes('--interactive') || args.includes('-i')) {
  interactiveSetup();
} else if (args.includes('--status') || args.includes('-s')) {
  showStatus();
} else if (args.includes('--example') || args.includes('-e')) {
  generateExample();
} else {
  main();
}

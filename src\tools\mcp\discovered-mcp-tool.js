import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import { MCPTransportFactory } from './mcp-transport.js';
import logger from '../../utils/logger.js';

/**
 * DiscoveredMCPTool - Executes tools from MCP servers
 */
export class DiscoveredMCPTool extends BaseTool {
  constructor(toolDefinition, serverConfig) {
    super({
      name: toolDefinition.name,
      description: toolDefinition.description || 'MCP server tool',
      category: ToolCategory.MCP,
      permissionLevel: PermissionLevel.DANGEROUS
    });
    
    this.toolDefinition = toolDefinition;
    this.serverConfig = serverConfig;
    this.transport = null;
    this.parameters = toolDefinition.inputSchema || {};
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: this.parameters
    };
  }

  /**
   * Validate tool parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate against tool definition schema
    if (this.parameters.required) {
      for (const requiredParam of this.parameters.required) {
        if (!(requiredParam in params)) {
          errors.push(`Missing required parameter: ${requiredParam}`);
        }
      }
    }

    // Validate parameter types if defined
    if (this.parameters.properties) {
      for (const [paramName, paramValue] of Object.entries(params)) {
        const paramDef = this.parameters.properties[paramName];
        if (paramDef && paramDef.type) {
          const actualType = typeof paramValue;
          const expectedType = paramDef.type;
          
          if (expectedType === 'integer' && !Number.isInteger(paramValue)) {
            errors.push(`Parameter ${paramName} must be an integer`);
          } else if (expectedType === 'number' && typeof paramValue !== 'number') {
            errors.push(`Parameter ${paramName} must be a number`);
          } else if (expectedType === 'string' && typeof paramValue !== 'string') {
            errors.push(`Parameter ${paramName} must be a string`);
          } else if (expectedType === 'boolean' && typeof paramValue !== 'boolean') {
            errors.push(`Parameter ${paramName} must be a boolean`);
          } else if (expectedType === 'array' && !Array.isArray(paramValue)) {
            errors.push(`Parameter ${paramName} must be an array`);
          } else if (expectedType === 'object' && (typeof paramValue !== 'object' || Array.isArray(paramValue))) {
            errors.push(`Parameter ${paramName} must be an object`);
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if operation is risky
   */
  isRiskyOperation(params) {
    // MCP tools are considered risky by default
    return true;
  }

  /**
   * Get confirmation message
   */
  getConfirmationMessage(params) {
    return `Execute MCP tool "${this.name}" from server "${this.serverConfig.name}"?\n\nDescription: ${this.description}\n\nParameters:\n${JSON.stringify(params, null, 2)}\n\nServer: ${this.serverConfig.type}://${this.serverConfig.url || this.serverConfig.command}`;
  }

  /**
   * Ensure connection to MCP server
   */
  async ensureConnection() {
    if (!this.transport) {
      this.transport = MCPTransportFactory.createTransport(this.serverConfig);
    }

    if (!this.transport.connected) {
      await this.transport.connect();
    }
  }

  /**
   * Execute the MCP tool
   */
  async execute(params) {
    try {
      logger.debug(`Executing MCP tool: ${this.name}`);
      
      // Ensure connection to MCP server
      await this.ensureConnection();

      // Call the tool on the MCP server
      const result = await this.transport.sendRequest('tools/call', {
        name: this.name,
        arguments: params
      });

      // Process the result
      const content = this.formatMCPResult(result);
      
      return ToolResult.success(content, {
        displayContent: content,
        metadata: {
          toolName: this.name,
          serverName: this.serverConfig.name,
          serverType: this.serverConfig.type,
          parameters: params,
          rawResult: result
        }
      });

    } catch (error) {
      logger.error(`MCP tool execution failed for ${this.name}:`, error.message);
      
      // Try to reconnect on connection errors
      if (error.message.includes('connection') || error.message.includes('timeout')) {
        try {
          if (this.transport) {
            await this.transport.disconnect();
            this.transport = null;
          }
        } catch (disconnectError) {
          logger.warn('Error during MCP transport cleanup:', disconnectError.message);
        }
      }

      return ToolResult.error(error, {
        metadata: {
          toolName: this.name,
          serverName: this.serverConfig.name,
          serverType: this.serverConfig.type,
          parameters: params
        }
      });
    }
  }

  /**
   * Format MCP result for display
   */
  formatMCPResult(result) {
    if (!result) {
      return 'MCP tool executed successfully with no result.';
    }

    // Handle different result formats
    if (typeof result === 'string') {
      return result;
    }

    if (result.content) {
      // Handle content array format
      if (Array.isArray(result.content)) {
        return result.content.map(item => {
          if (item.type === 'text') {
            return item.text;
          } else if (item.type === 'json') {
            return JSON.stringify(item.json, null, 2);
          } else {
            return JSON.stringify(item, null, 2);
          }
        }).join('\n\n');
      }
      
      return result.content;
    }

    // Fallback to JSON representation
    return JSON.stringify(result, null, 2);
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    if (this.transport && this.transport.connected) {
      try {
        await this.transport.disconnect();
      } catch (error) {
        logger.warn('Error disconnecting MCP transport:', error.message);
      }
    }
  }

  /**
   * Get tool information
   */
  getInfo() {
    return {
      name: this.name,
      description: this.description,
      category: this.category,
      permissionLevel: this.permissionLevel,
      parameters: this.parameters,
      serverConfig: {
        name: this.serverConfig.name,
        type: this.serverConfig.type,
        url: this.serverConfig.url,
        command: this.serverConfig.command
      },
      isMCP: true
    };
  }
}

/**
 * MCP Server Manager - Manages connections to MCP servers and tool discovery
 */
export class MCPServerManager {
  constructor() {
    this.servers = new Map();
    this.discoveredTools = new Map();
    this.allowlist = new Set();
    this.connections = new Map();
  }

  /**
   * Add MCP server configuration
   */
  addServer(name, config) {
    const requiredFields = ['type'];
    for (const field of requiredFields) {
      if (!config[field]) {
        throw new Error(`Server config missing required field: ${field}`);
      }
    }

    // Validate transport-specific requirements
    if (config.type === 'stdio' && !config.command) {
      throw new Error('Stdio server config requires command field');
    }
    
    if ((config.type === 'sse' || config.type === 'http') && !config.url) {
      throw new Error(`${config.type.toUpperCase()} server config requires url field`);
    }

    this.servers.set(name, {
      name,
      ...config,
      enabled: config.enabled !== false
    });

    logger.debug(`Added MCP server config: ${name}`);
  }

  /**
   * Add server/tool to allowlist
   */
  addToAllowlist(serverName, toolName = null) {
    const key = toolName ? `${serverName}:${toolName}` : serverName;
    this.allowlist.add(key);
    logger.debug(`Added to MCP allowlist: ${key}`);
  }

  /**
   * Check if server/tool is allowed
   */
  isAllowed(serverName, toolName = null) {
    const serverKey = serverName;
    const toolKey = toolName ? `${serverName}:${toolName}` : null;
    
    return this.allowlist.has(serverKey) || (toolKey && this.allowlist.has(toolKey));
  }

  /**
   * Discover tools from MCP servers
   */
  async discoverTools(serverName = null) {
    const servers = serverName 
      ? [this.servers.get(serverName)]
      : Array.from(this.servers.values());

    const discoveredTools = [];

    for (const serverConfig of servers) {
      if (!serverConfig || !serverConfig.enabled) continue;

      try {
        logger.debug(`Discovering tools from MCP server: ${serverConfig.name}`);
        
        // Create transport and connect
        const transport = MCPTransportFactory.createTransport(serverConfig);
        await transport.connect();
        
        this.connections.set(serverConfig.name, transport);

        // List available tools
        const toolsResult = await transport.sendRequest('tools/list');
        
        if (toolsResult && toolsResult.tools) {
          for (const toolDef of toolsResult.tools) {
            const mcpTool = new DiscoveredMCPTool(toolDef, serverConfig);
            this.discoveredTools.set(`${serverConfig.name}:${toolDef.name}`, mcpTool);
            discoveredTools.push(mcpTool);
            
            logger.debug(`Discovered MCP tool: ${toolDef.name} from ${serverConfig.name}`);
          }
        }

      } catch (error) {
        logger.error(`MCP tool discovery failed for server ${serverConfig.name}:`, error.message);
      }
    }

    return discoveredTools;
  }

  /**
   * Get discovered tool by name
   */
  getTool(serverName, toolName) {
    const key = `${serverName}:${toolName}`;
    return this.discoveredTools.get(key);
  }

  /**
   * Get all discovered tools
   */
  getAllTools() {
    return Array.from(this.discoveredTools.values());
  }

  /**
   * Get tools from specific server
   */
  getServerTools(serverName) {
    return Array.from(this.discoveredTools.values())
      .filter(tool => tool.serverConfig.name === serverName);
  }

  /**
   * Disconnect from all servers
   */
  async disconnectAll() {
    const disconnectPromises = [];
    
    for (const [serverName, transport] of this.connections) {
      disconnectPromises.push(
        transport.disconnect().catch(error => {
          logger.warn(`Error disconnecting from ${serverName}:`, error.message);
        })
      );
    }

    await Promise.all(disconnectPromises);
    this.connections.clear();
    
    logger.debug('Disconnected from all MCP servers');
  }

  /**
   * Clear discovered tools
   */
  clearTools() {
    this.discoveredTools.clear();
  }

  /**
   * Get server statistics
   */
  getStats() {
    return {
      totalServers: this.servers.size,
      connectedServers: this.connections.size,
      discoveredTools: this.discoveredTools.size,
      allowlistEntries: this.allowlist.size,
      servers: Array.from(this.servers.values()).map(server => ({
        name: server.name,
        type: server.type,
        enabled: server.enabled,
        connected: this.connections.has(server.name),
        toolCount: this.getServerTools(server.name).length
      }))
    };
  }
}

export default new MCPServerManager();

# Function Tools Architecture

A comprehensive tool system built around the `BaseTool` class with centralized `ToolRegistry` management. All tools follow a consistent lifecycle pattern with validation, confirmation (when needed), and execution phases.

## Architecture Overview

### Core Components

- **BaseTool**: Abstract base class for all tools with lifecycle management
- **ToolRegistry**: Central registry for tool management and execution
- **ToolResult**: Standardized result format for all tool operations
- **Tool Categories**: Filesystem, Execution, Web, Memory, MCP, Utility

### Tool Lifecycle

1. **Registration**: Tools register with `ToolRegistry`
2. **Model Request**: LLM decides to use a tool
3. **Parameter Validation**: `validateToolParams()` checks parameters
4. **Confirmation** (if needed): `shouldConfirmExecute()` prompts user
5. **Execution**: `execute()` performs the actual work
6. **Result Processing**: Returns `ToolResult` with LLM content and display info

## Available Tools

### File System Tools

#### LSTool (`list_directory`)
Lists directory contents with filtering and sorting options.

**Parameters:**
- `path` (required): Absolute directory path
- `ignore` (optional): Array of glob patterns to ignore
- `respect_git_ignore` (optional): Whether to respect .gitignore
- `recursive` (optional): Whether to list recursively
- `show_hidden` (optional): Whether to show hidden files

#### ReadFileTool (`read_file`)
Reads content from individual files with support for text, images, and PDFs.

**Parameters:**
- `absolute_path` (required): Absolute file path
- `offset` (optional): Starting line number for pagination
- `limit` (optional): Number of lines to read

#### WriteFileTool (`write_file`)
Creates new files or overwrites existing ones with diff preview.

**Parameters:**
- `file_path` (required): Absolute path for file
- `content` (required): Content to write
- `modified_by_user` (optional): Flag if user modified content

#### EditTool (`replace`)
Performs precise text replacements in files with validation.

**Parameters:**
- `file_path` (required): Absolute file path
- `old_string` (required): Exact text to replace
- `new_string` (required): Replacement text
- `expected_replacements` (optional): Expected number of replacements

#### GlobTool (`glob`)
Finds files matching glob patterns with sorting and filtering.

**Parameters:**
- `pattern` (required): Glob pattern (e.g., `**/*.ts`)
- `path` (optional): Search directory
- `case_sensitive` (optional): Case sensitivity flag
- `respect_git_ignore` (optional): Git ignore respect flag

#### GrepTool (`search_file_content`)
Searches for regex patterns within file contents using multiple strategies.

**Parameters:**
- `pattern` (required): Regular expression pattern
- `path` (optional): Directory to search
- `include` (optional): File pattern filter

#### ReadManyFilesTool (`read_many_files`)
Reads and concatenates multiple files with filtering.

**Parameters:**
- `paths` (required): Array of glob patterns/paths
- `include` (optional): Additional include patterns
- `exclude` (optional): Exclusion patterns
- `recursive` (optional): Recursive search flag

### Execution Tools

#### ShellTool (`run_shell_command`)
Executes shell commands with sandboxing and safety checks.

**Parameters:**
- `command` (required): Shell command to execute
- `description` (optional): User-friendly description
- `directory` (optional): Working directory
- `timeout` (optional): Command timeout in milliseconds

### Web Tools

#### WebFetchTool (`web_fetch`)
Fetches and processes content from URLs with multiple strategies.

**Parameters:**
- `prompt` (required): Text containing URLs and processing instructions
- `extract_text_only` (optional): Whether to extract only text content
- `follow_redirects` (optional): Whether to follow HTTP redirects
- `timeout` (optional): Request timeout in milliseconds

#### WebSearchTool (`google_web_search`)
Performs web searches using multiple search APIs.

**Parameters:**
- `query` (required): Search query string
- `num_results` (optional): Number of results to return
- `safe_search` (optional): Safe search setting
- `country` (optional): Country code for localized results
- `language` (optional): Language code for results

**Supported Search APIs:**
- DuckDuckGo (free, no API key required)
- SerpAPI (requires `SERPAPI_KEY` environment variable)
- Bing Search API (requires `BING_SEARCH_API_KEY` environment variable)

### Memory Tools

#### MemoryTool (`save_memory`)
Saves information to persistent AI memory.

**Parameters:**
- `fact` (required): Information to remember

### MCP Tools

#### DiscoveredTool (Dynamic Tool Discovery)
Executes custom tools discovered via shell commands.

**Configuration:**
- `toolDiscoveryCommand`: Command to discover available tools
- `toolCallCommand`: Command template to execute tools
- `workingDirectory`: Working directory for commands
- `timeout`: Execution timeout

#### DiscoveredMCPTool (MCP Server Tools)
Executes tools from MCP servers with multiple transport types.

**Supported Transports:**
- **Stdio**: Command-line MCP servers
- **SSE**: Server-Sent Events over HTTP
- **HTTP**: Streamable HTTP transport

## Configuration

### Environment Variables

```bash
# Web Search APIs
SERPAPI_KEY=your_serpapi_key_here
BING_SEARCH_API_KEY=your_bing_api_key_here

# Tool Discovery
TOOL_DISCOVERY_ENABLED=true

# MCP Configuration
MCP_FILESYSTEM_ENABLED=true
MCP_ALLOWLIST=server1,server2:tool1,server3:tool2
```

### Programmatic Configuration

```javascript
import { 
  initializeTools, 
  configureToolDiscovery, 
  configureMCPServers, 
  addToMCPAllowlist 
} from './tools/index.js';

// Configure tool discovery
configureToolDiscovery({
  'my-tools': {
    toolDiscoveryCommand: 'my-tool-manager list --json',
    toolCallCommand: 'my-tool-manager execute <toolName>',
    workingDirectory: '/path/to/tools',
    enabled: true
  }
});

// Configure MCP servers
configureMCPServers({
  'filesystem': {
    type: 'stdio',
    command: 'mcp-server-filesystem',
    args: ['--root', '/path/to/files'],
    enabled: true
  },
  'api-server': {
    type: 'http',
    url: 'https://api.example.com/mcp',
    headers: { 'Authorization': 'Bearer token' },
    enabled: true
  }
});

// Configure MCP allowlist
addToMCPAllowlist('filesystem'); // Allow all tools from server
addToMCPAllowlist('api-server', 'safe-tool'); // Allow specific tool

// Initialize all tools
await initializeTools();
```

### Configuration File

Use the configuration utilities:

```javascript
import toolConfig from './tools/config/tool-config.js';

// Load and apply configuration
const config = toolConfig.loadToolConfig();
toolConfig.applyToolConfig(config);

// Run setup wizard
await toolConfig.setupWizard();

// Generate example configuration
const exampleConfig = toolConfig.generateExampleConfig();
console.log(JSON.stringify(exampleConfig, null, 2));
```

## Usage Examples

### Basic Tool Execution

```javascript
import { executeTool, getToolRegistry } from './tools/index.js';

// Execute a tool
const result = await executeTool('list_directory', {
  path: '/path/to/directory',
  recursive: true
});

console.log(result.content); // LLM-formatted content
console.log(result.getDisplayContent()); // User-formatted content

// Get tool registry
const registry = getToolRegistry();
const allTools = registry.getAllTools();
const functionDefs = registry.getFunctionDefinitions();
```

### Custom Tool Development

```javascript
import { BaseTool, ToolResult, ToolCategory, PermissionLevel } from './tools/index.js';

class MyCustomTool extends BaseTool {
  constructor() {
    super({
      name: 'my_custom_tool',
      description: 'Does something custom',
      category: ToolCategory.UTILITY,
      permissionLevel: PermissionLevel.SAFE
    });
  }

  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          input: { type: 'string', description: 'Input parameter' }
        },
        required: ['input']
      }
    };
  }

  async validateParams(params) {
    const errors = [];
    if (!params.input || typeof params.input !== 'string') {
      errors.push('input must be a non-empty string');
    }
    return { isValid: errors.length === 0, errors };
  }

  async execute(params) {
    // Your custom logic here
    const result = `Processed: ${params.input}`;
    return ToolResult.success(result);
  }
}

// Register the custom tool
import { getToolRegistry } from './tools/index.js';
getToolRegistry().register(new MyCustomTool());
```

## Security Features

- **Permission Levels**: SAFE, MODERATE, DANGEROUS
- **Command Whitelisting**: Only approved commands can be executed
- **Path Validation**: All file operations are validated against root directory
- **MCP Allowlisting**: Control which MCP servers and tools are allowed
- **Confirmation Prompts**: Dangerous operations require user confirmation
- **Sandboxing**: Shell commands run in controlled environments

## Error Handling

All tools return standardized `ToolResult` objects with:
- Success/error status
- Content for LLM consumption
- Display content for users
- Metadata and execution details
- Proper error messages and stack traces

## Performance Features

- **Caching**: File operations and search results are cached
- **Streaming**: Large operations support streaming output
- **Timeouts**: All operations have configurable timeouts
- **Resource Limits**: File size and output length limits
- **Connection Pooling**: MCP connections are reused efficiently

## Extensibility

The tool system is designed for easy extension:
- Add new tool categories
- Implement custom transport types for MCP
- Create domain-specific tool collections
- Integrate with external APIs and services
- Build tool discovery mechanisms for specific environments

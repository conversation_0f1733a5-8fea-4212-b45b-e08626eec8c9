#!/usr/bin/env node

/**
 * Tool System Demonstration
 * 
 * This script demonstrates the LLM CLI tool system capabilities
 * without requiring an actual LLM provider API key.
 */

import { initializeTools, executeTool, getToolRegistry } from '../src/tools/index.js';
import chalk from 'chalk';
import path from 'path';

async function demonstrateTools() {
  console.log(chalk.bold.blue('🚀 LLM CLI Tool System Demonstration'));
  console.log();

  try {
    // Initialize the tool system
    console.log(chalk.cyan('1. Initializing tool system...'));
    await initializeTools();
    console.log(chalk.green('✓ Tool system initialized'));
    console.log();

    // Show available tools
    const registry = getToolRegistry();
    const stats = registry.getStats();
    console.log(chalk.cyan('2. Available tools:'));
    console.log(`   Total: ${chalk.bold(stats.totalTools)} tools`);
    console.log(`   Categories: ${chalk.bold(Object.keys(stats.categories).length)}`);
    
    for (const [category, count] of Object.entries(stats.categories)) {
      console.log(`   • ${category}: ${chalk.yellow(count)} tools`);
    }
    console.log();

    // Demonstrate file system tools
    console.log(chalk.cyan('3. Demonstrating file system tools:'));
    console.log();

    // List current directory
    console.log(chalk.yellow('📁 Listing current directory:'));
    try {
      const listResult = await executeTool('list_directory', {
        path: process.cwd()
      });
      
      if (listResult.isSuccess()) {
        console.log(chalk.green('✓ Directory listing successful'));
        // Show first few lines
        const lines = listResult.content.split('\n').slice(0, 5);
        lines.forEach(line => console.log(chalk.gray(`   ${line}`)));
        if (listResult.content.split('\n').length > 5) {
          console.log(chalk.gray('   ... (truncated)'));
        }
      }
    } catch (error) {
      console.log(chalk.red('✗ Directory listing failed:'), error.message);
    }
    console.log();

    // Find JavaScript files
    console.log(chalk.yellow('🔍 Finding JavaScript files:'));
    try {
      const globResult = await executeTool('glob', {
        pattern: '**/*.js',
        max_results: 5
      });
      
      if (globResult.isSuccess()) {
        console.log(chalk.green('✓ File search successful'));
        const metadata = globResult.metadata;
        console.log(chalk.gray(`   Found ${metadata.totalFound} files, showing ${metadata.returned}`));
        
        if (metadata.returned > 0) {
          console.log(chalk.gray('   Sample files:'));
          const lines = globResult.content.split('\n');
          const fileLines = lines.filter(line => line.trim().startsWith('src/') || line.trim().startsWith('examples/'));
          fileLines.slice(0, 3).forEach(line => {
            if (line.trim()) console.log(chalk.gray(`     ${line.trim()}`));
          });
        }
      }
    } catch (error) {
      console.log(chalk.red('✗ File search failed:'), error.message);
    }
    console.log();

    // Read package.json
    console.log(chalk.yellow('📄 Reading package.json:'));
    try {
      const readResult = await executeTool('read_file', {
        absolute_path: path.join(process.cwd(), 'package.json'),
        limit: 10
      });
      
      if (readResult.isSuccess()) {
        console.log(chalk.green('✓ File read successful'));
        console.log(chalk.gray('   First 10 lines:'));
        const lines = readResult.content.split('\n').slice(3, 8); // Skip header lines
        lines.forEach(line => {
          if (line.trim()) console.log(chalk.gray(`     ${line}`));
        });
      }
    } catch (error) {
      console.log(chalk.red('✗ File read failed:'), error.message);
    }
    console.log();

    // Demonstrate memory tool
    console.log(chalk.cyan('4. Demonstrating memory tool:'));
    console.log();

    console.log(chalk.yellow('🧠 Saving a memory:'));
    try {
      const memoryResult = await executeTool('save_memory', {
        fact: 'LLM CLI tool system demonstration completed successfully',
        category: 'Demo',
        importance: 'medium',
        tags: ['demo', 'tools', 'cli']
      });
      
      if (memoryResult.isSuccess()) {
        console.log(chalk.green('✓ Memory saved successfully'));
        console.log(chalk.gray(`   Memory ID: ${memoryResult.metadata.entryId}`));
        console.log(chalk.gray(`   Category: ${memoryResult.metadata.category}`));
        console.log(chalk.gray(`   Tags: ${memoryResult.metadata.tags.join(', ')}`));
      }
    } catch (error) {
      console.log(chalk.red('✗ Memory save failed:'), error.message);
    }
    console.log();

    // Show tool safety information
    console.log(chalk.cyan('5. Tool safety information:'));
    console.log();

    const tools = registry.getAllTools();
    const safeTools = tools.filter(t => t.permissionLevel === 'safe').length;
    const moderateTools = tools.filter(t => t.permissionLevel === 'moderate').length;
    const dangerousTools = tools.filter(t => t.permissionLevel === 'dangerous').length;

    console.log(`${chalk.green('Safe tools')}: ${safeTools} (no confirmation needed)`);
    console.log(`${chalk.yellow('Moderate tools')}: ${moderateTools} (confirmation recommended)`);
    console.log(`${chalk.red('Dangerous tools')}: ${dangerousTools} (always require confirmation)`);
    console.log();

    // Summary
    console.log(chalk.bold.green('🎉 Tool system demonstration completed!'));
    console.log();
    console.log(chalk.gray('Key features demonstrated:'));
    console.log(chalk.gray('  • Tool initialization and registration'));
    console.log(chalk.gray('  • File system operations (list, read, search)'));
    console.log(chalk.gray('  • Memory management'));
    console.log(chalk.gray('  • Safety levels and validation'));
    console.log(chalk.gray('  • Error handling and graceful degradation'));
    console.log();
    console.log(chalk.cyan('Next steps:'));
    console.log(chalk.gray('  • Configure an LLM provider: npm start config'));
    console.log(chalk.gray('  • Start interactive chat: npm start'));
    console.log(chalk.gray('  • Try asking the AI to use tools in conversation!'));

  } catch (error) {
    console.log(chalk.red('❌ Demonstration failed:'), error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the demonstration
demonstrateTools().catch(console.error);

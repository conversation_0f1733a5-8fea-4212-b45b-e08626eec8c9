# LLM CLI

A modern CLI terminal interface for interacting with multiple LLM providers including OpenAI, Anthropic, and DeepSeek.

## Features

- **Multiple LLM Providers**: Support for OpenAI, Anthropic, and DeepSeek
- **Interactive Configuration**: Easy setup with guided configuration screens
- **Interactive Chat**: Real-time conversation with your chosen LLM
- **Function Tools**: Comprehensive tool system for file operations, web access, and more
- **Model Selection**: Choose from available models for each provider
- **Customizable Settings**: Configure temperature, max tokens, and system prompts
- **Connection Testing**: Test API connections before use
- **Token Usage**: Track token consumption (when supported)
- **Conversation History**: Save and manage chat conversations
- **Safe Execution**: Sandboxed tool execution with permission levels
- **Advanced Search**: File content search, glob patterns, and web fetching
- **Memory System**: Persistent AI memory for context retention
- **Modern UI**: Beautiful terminal interface with colors and formatting

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd llm-cli
```

2. Install dependencies:
```bash
npm install
```

3. Make the CLI globally available (optional):
```bash
npm link
```

## Quick Start

1. **Start Interactive Mode** (recommended for first-time setup):
```bash
npm start
# or if globally installed:
llm-cli
```

2. **Configure a Provider**:
   - Select "Configure Provider" from the main menu
   - Choose your preferred provider (OpenAI, Anthropic, or DeepSeek)
   - Enter your API key
   - Configure model settings (optional)

3. **Start Chatting**:
   - Select "Start Chat" from the main menu
   - Begin your conversation!

## Usage

### Interactive Mode
```bash
llm-cli interactive
# or simply
llm-cli
```

### Configuration
```bash
# Open configuration screen
llm-cli config

# Configure specific provider
llm-cli config --provider openai --api-key sk-your-key

# List all providers
llm-cli config --list

# Test connections
llm-cli config --test

# Show status
llm-cli config --status
```

### Chat
```bash
# Start interactive chat
llm-cli chat

# Send single message
llm-cli chat "Hello, how are you?"

# Use specific model
llm-cli chat "Explain quantum computing" --model gpt-4

# Set custom temperature
llm-cli chat "Write a poem" --temperature 1.2

# Show token usage
llm-cli chat "Hello" --tokens
```

### Models
```bash
# List available models for active provider
llm-cli models
```

### Providers
```bash
# List all providers
llm-cli providers --list

# Test all configured providers
llm-cli providers --test
```

### Tools
```bash
# List all available tools
llm-cli tools --list

# Show tool statistics
llm-cli tools --stats

# List tool categories
llm-cli tools --categories

# Test tool system
llm-cli tools --test

# Initialize tool system
llm-cli tools --init
```

## API Keys

You'll need API keys from the providers you want to use:

### OpenAI
1. Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Sign in to your account
3. Click "Create new secret key"
4. Copy the key (starts with "sk-")

### Anthropic
1. Go to [Anthropic Console](https://console.anthropic.com/settings/keys)
2. Sign in to your account
3. Click "Create Key"
4. Copy the key (starts with "sk-ant-")

### DeepSeek
1. Go to [DeepSeek Platform](https://platform.deepseek.com/api_keys)
2. Sign in to your account
3. Click "Create API Key"
4. Copy the key (starts with "sk-")

## Chat Commands

While in chat mode, you can use these commands:

- `/help` - Show available commands
- `/model` - Change the current model
- `/tools` - Show tool system status
- `/clear` - Clear conversation history
- `/config` - Open configuration screen
- `/history` - Show conversation history
- `/save [filename]` - Save conversation to file
- `/exit` or `/quit` - Exit chat

## Function Tools System

LLM CLI includes a comprehensive function tools system that allows AI models to interact with your file system, web resources, and more. Tools are executed safely with permission levels and user confirmation when needed.

### Available Tool Categories

#### File System Tools
- **list_directory**: List directory contents with filtering
- **read_file**: Read text files, images, and PDFs
- **write_file**: Create or overwrite files with diff preview
- **replace**: Perform precise text replacements in files
- **glob**: Find files using glob patterns
- **search_file_content**: Search for regex patterns in files
- **read_many_files**: Read and concatenate multiple files

#### Execution Tools
- **run_shell_command**: Execute shell commands with sandboxing

#### Web Tools
- **web_fetch**: Fetch content from URLs
- **google_web_search**: Perform web searches

#### Memory Tools
- **save_memory**: Save information to persistent AI memory

### Tool Safety

Tools are categorized by safety level:
- **Safe**: No confirmation needed (e.g., reading files, listing directories)
- **Moderate**: Confirmation recommended (e.g., writing files, web requests)
- **Dangerous**: Always require confirmation (e.g., shell commands)

### Tool Configuration

Tools can be configured in the settings:

```json
{
  "tools": {
    "enabled": true,
    "autoConfirm": false,
    "safeMode": true,
    "maxFileSize": 10485760,
    "maxFiles": 100,
    "timeout": 30000,
    "respectGitIgnore": true
  }
}
```

## Configuration

Configuration is stored locally using the `conf` package. The configuration includes:

- **Provider Settings**: API keys, models, and preferences for each provider
- **Active Provider**: Currently selected provider
- **UI Settings**: Theme, display preferences
- **Chat Settings**: System prompt, token display, history settings

## Project Structure

```
llm-cli/
├── src/
│   ├── cli/
│   │   ├── commands/          # CLI command implementations
│   │   └── ui/                # User interface components
│   │       ├── components/    # Reusable UI components
│   │       ├── config-screen.js
│   │       └── chat-interface.js
│   ├── providers/             # LLM provider implementations
│   │   ├── base-provider.js
│   │   ├── openai.js
│   │   ├── anthropic.js
│   │   ├── deepseek.js
│   │   └── index.js
│   ├── tools/                 # Function tools system
│   │   ├── base/              # Base classes and registry
│   │   ├── filesystem/        # File system tools
│   │   ├── execution/         # Command execution tools
│   │   ├── web/               # Web access tools
│   │   ├── memory/            # Memory management tools
│   │   ├── utils/             # Tool utilities
│   │   └── index.js
│   ├── config/                # Configuration management
│   │   ├── config-manager.js
│   │   └── default-config.js
│   ├── utils/                 # Utility functions
│   │   ├── logger.js
│   │   └── validation.js
│   └── index.js               # Main entry point
├── package.json
└── README.md
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Troubleshooting

### Common Issues

1. **"No provider configured"**
   - Run `llm-cli config` to set up a provider
   - Make sure you have a valid API key

2. **"API key test failed"**
   - Verify your API key is correct
   - Check your internet connection
   - Ensure you have sufficient credits/quota

3. **"Command not found"**
   - Make sure you've installed dependencies with `npm install`
   - If using globally, run `npm link` in the project directory

### Debug Mode

Enable verbose logging for troubleshooting:
```bash
llm-cli --verbose
```

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the provider documentation
3. Open an issue on GitHub

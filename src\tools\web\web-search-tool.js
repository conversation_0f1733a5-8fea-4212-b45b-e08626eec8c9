import axios from 'axios';
import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import logger from '../../utils/logger.js';

/**
 * WebSearchTool - Performs web searches using Google Search
 */
export class WebSearchTool extends BaseTool {
  constructor() {
    super({
      name: 'google_web_search',
      description: 'Performs web searches using Google Search with result processing',
      category: ToolCategory.WEB,
      permissionLevel: PermissionLevel.MODERATE
    });
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query string'
          },
          num_results: {
            type: 'integer',
            description: 'Number of search results to return',
            minimum: 1,
            maximum: 20,
            default: 10
          },
          safe_search: {
            type: 'string',
            description: 'Safe search setting',
            enum: ['off', 'moderate', 'strict'],
            default: 'moderate'
          },
          country: {
            type: 'string',
            description: 'Country code for localized results (e.g., "us", "uk", "ca")',
            default: 'us'
          },
          language: {
            type: 'string',
            description: 'Language code for results (e.g., "en", "es", "fr")',
            default: 'en'
          },
          time_range: {
            type: 'string',
            description: 'Time range for results',
            enum: ['any', 'day', 'week', 'month', 'year'],
            default: 'any'
          }
        },
        required: ['query']
      }
    };
  }

  /**
   * Validate parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate query
    if (!params.query) {
      errors.push('query is required');
    } else if (typeof params.query !== 'string') {
      errors.push('query must be a string');
    } else if (params.query.trim() === '') {
      errors.push('query cannot be empty');
    } else if (params.query.length > 500) {
      errors.push('query must be 500 characters or less');
    }

    // Validate num_results
    if (params.num_results !== undefined) {
      if (!Number.isInteger(params.num_results) || params.num_results < 1 || params.num_results > 20) {
        errors.push('num_results must be an integer between 1 and 20');
      }
    }

    // Validate safe_search
    if (params.safe_search !== undefined) {
      const validSafeSearch = ['off', 'moderate', 'strict'];
      if (!validSafeSearch.includes(params.safe_search)) {
        errors.push(`safe_search must be one of: ${validSafeSearch.join(', ')}`);
      }
    }

    // Validate country
    if (params.country !== undefined) {
      if (typeof params.country !== 'string' || params.country.length !== 2) {
        errors.push('country must be a 2-character country code');
      }
    }

    // Validate language
    if (params.language !== undefined) {
      if (typeof params.language !== 'string' || params.language.length !== 2) {
        errors.push('language must be a 2-character language code');
      }
    }

    // Validate time_range
    if (params.time_range !== undefined) {
      const validTimeRanges = ['any', 'day', 'week', 'month', 'year'];
      if (!validTimeRanges.includes(params.time_range)) {
        errors.push(`time_range must be one of: ${validTimeRanges.join(', ')}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if operation is risky
   */
  isRiskyOperation(params) {
    // Check for potentially sensitive queries
    const sensitivePatterns = [
      /password/i,
      /credit card/i,
      /social security/i,
      /personal information/i,
      /hack/i,
      /exploit/i
    ];

    return sensitivePatterns.some(pattern => pattern.test(params.query));
  }

  /**
   * Get confirmation message
   */
  getConfirmationMessage(params) {
    const lines = [];
    
    lines.push(`Perform web search for: "${params.query}"?`);
    lines.push(`Results: ${params.num_results || 10}`);
    lines.push(`Safe search: ${params.safe_search || 'moderate'}`);
    lines.push(`Country: ${params.country || 'us'}`);
    lines.push(`Language: ${params.language || 'en'}`);
    
    if (params.time_range && params.time_range !== 'any') {
      lines.push(`Time range: ${params.time_range}`);
    }

    if (this.isRiskyOperation(params)) {
      lines.push('');
      lines.push('⚠️  WARNING: Query may contain sensitive terms!');
    }

    return lines.join('\n');
  }

  /**
   * Execute web search
   */
  async execute(params) {
    const {
      query,
      num_results = 10,
      safe_search = 'moderate',
      country = 'us',
      language = 'en',
      time_range = 'any'
    } = params;

    try {
      logger.debug(`Performing web search: ${query}`);

      // Note: This is a placeholder implementation
      // In a real implementation, you would integrate with:
      // 1. Google Custom Search API
      // 2. Bing Search API
      // 3. DuckDuckGo API
      // 4. Or use a service like SerpAPI

      const searchResults = await this.performSearch(query, {
        numResults: num_results,
        safeSearch: safe_search,
        country,
        language,
        timeRange: time_range
      });

      // Process and format results
      const processedResults = this.processSearchResults(searchResults);

      // Create result content
      const content = this.formatSearchResults(query, processedResults);
      
      // Create display content
      const displayContent = this.createDisplayContent(query, processedResults, {
        options: params
      });

      const metadata = {
        query,
        totalResults: processedResults.length,
        searchTime: Date.now(),
        options: {
          numResults: num_results,
          safeSearch: safe_search,
          country,
          language,
          timeRange: time_range
        }
      };

      return ToolResult.success(content, {
        displayContent,
        metadata
      });

    } catch (error) {
      logger.error(`Web search failed for query "${query}":`, error.message);
      return ToolResult.error(error);
    }
  }

  /**
   * Perform the actual search using multiple strategies
   */
  async performSearch(query, options) {
    // Strategy 1: Try DuckDuckGo Instant Answer API (free, no API key required)
    try {
      const duckDuckGoResults = await this.searchWithDuckDuckGo(query, options);
      if (duckDuckGoResults && duckDuckGoResults.length > 0) {
        logger.debug('Using DuckDuckGo search results');
        return duckDuckGoResults;
      }
    } catch (error) {
      logger.debug('DuckDuckGo search failed:', error.message);
    }

    // Strategy 2: Try SerpAPI (requires API key)
    if (process.env.SERPAPI_KEY) {
      try {
        const serpResults = await this.searchWithSerpAPI(query, options);
        if (serpResults && serpResults.length > 0) {
          logger.debug('Using SerpAPI search results');
          return serpResults;
        }
      } catch (error) {
        logger.debug('SerpAPI search failed:', error.message);
      }
    }

    // Strategy 3: Try Bing Search API (requires API key)
    if (process.env.BING_SEARCH_API_KEY) {
      try {
        const bingResults = await this.searchWithBing(query, options);
        if (bingResults && bingResults.length > 0) {
          logger.debug('Using Bing search results');
          return bingResults;
        }
      } catch (error) {
        logger.debug('Bing search failed:', error.message);
      }
    }

    // Fallback: Return informative message about search configuration
    logger.warn('No search APIs configured. Set SERPAPI_KEY or BING_SEARCH_API_KEY environment variables for real search results.');

    return [{
      title: `Search Configuration Required for "${query}"`,
      url: 'https://github.com/your-repo/search-setup',
      snippet: `To enable real web search results, configure one of the supported search APIs by setting environment variables: SERPAPI_KEY (for SerpAPI) or BING_SEARCH_API_KEY (for Bing Search API). Currently showing placeholder result for query: "${query}".`,
      displayUrl: 'github.com',
      publishedDate: new Date().toISOString()
    }];
  }

  /**
   * Search using DuckDuckGo Instant Answer API
   */
  async searchWithDuckDuckGo(query, options) {
    try {
      const axios = (await import('axios')).default;

      // DuckDuckGo Instant Answer API (limited but free)
      const response = await axios.get('https://api.duckduckgo.com/', {
        params: {
          q: query,
          format: 'json',
          no_html: '1',
          skip_disambig: '1'
        },
        timeout: 10000
      });

      const results = [];
      const data = response.data;

      // Add instant answer if available
      if (data.Abstract && data.AbstractText) {
        results.push({
          title: data.Heading || `DuckDuckGo: ${query}`,
          url: data.AbstractURL || 'https://duckduckgo.com',
          snippet: data.AbstractText,
          displayUrl: 'duckduckgo.com',
          publishedDate: new Date().toISOString()
        });
      }

      // Add related topics
      if (data.RelatedTopics && data.RelatedTopics.length > 0) {
        for (const topic of data.RelatedTopics.slice(0, options.numResults - results.length)) {
          if (topic.Text && topic.FirstURL) {
            results.push({
              title: topic.Text.split(' - ')[0] || topic.Text.substring(0, 100),
              url: topic.FirstURL,
              snippet: topic.Text,
              displayUrl: this.extractDomain(topic.FirstURL),
              publishedDate: new Date().toISOString()
            });
          }
        }
      }

      return results.slice(0, options.numResults);

    } catch (error) {
      logger.debug('DuckDuckGo API error:', error.message);
      return [];
    }
  }

  /**
   * Search using SerpAPI
   */
  async searchWithSerpAPI(query, options) {
    try {
      const axios = (await import('axios')).default;

      const response = await axios.get('https://serpapi.com/search', {
        params: {
          q: query,
          api_key: process.env.SERPAPI_KEY,
          engine: 'google',
          num: options.numResults,
          safe: options.safeSearch,
          gl: options.country,
          hl: options.language
        },
        timeout: 15000
      });

      const results = [];
      const organicResults = response.data.organic_results || [];

      for (const result of organicResults) {
        results.push({
          title: result.title,
          url: result.link,
          snippet: result.snippet,
          displayUrl: result.displayed_link || this.extractDomain(result.link),
          publishedDate: result.date || new Date().toISOString()
        });
      }

      return results;

    } catch (error) {
      logger.debug('SerpAPI error:', error.message);
      return [];
    }
  }

  /**
   * Search using Bing Search API
   */
  async searchWithBing(query, options) {
    try {
      const axios = (await import('axios')).default;

      const response = await axios.get('https://api.bing.microsoft.com/v7.0/search', {
        params: {
          q: query,
          count: options.numResults,
          safeSearch: options.safeSearch,
          cc: options.country,
          setLang: options.language,
          freshness: options.timeRange
        },
        headers: {
          'Ocp-Apim-Subscription-Key': process.env.BING_SEARCH_API_KEY
        },
        timeout: 15000
      });

      const results = [];
      const webPages = response.data.webPages?.value || [];

      for (const page of webPages) {
        results.push({
          title: page.name,
          url: page.url,
          snippet: page.snippet,
          displayUrl: page.displayUrl || this.extractDomain(page.url),
          publishedDate: page.dateLastCrawled || new Date().toISOString()
        });
      }

      return results;

    } catch (error) {
      logger.debug('Bing Search API error:', error.message);
      return [];
    }
  }

  /**
   * Process search results
   */
  processSearchResults(rawResults) {
    return rawResults.map((result, index) => ({
      rank: index + 1,
      title: result.title,
      url: result.url,
      snippet: result.snippet,
      displayUrl: result.displayUrl || this.extractDomain(result.url),
      publishedDate: result.publishedDate,
      citationId: `[${index + 1}]`
    }));
  }

  /**
   * Extract domain from URL
   */
  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      return url;
    }
  }

  /**
   * Format search results for LLM
   */
  formatSearchResults(query, results) {
    const lines = [];
    
    lines.push(`Web search results for: "${query}"`);
    lines.push(`Found ${results.length} results`);
    lines.push('');

    if (results.length === 0) {
      lines.push('No search results found.');
      return lines.join('\n');
    }

    for (const result of results) {
      lines.push(`${result.rank}. ${result.title}`);
      lines.push(`   URL: ${result.url}`);
      lines.push(`   ${result.snippet}`);
      lines.push('');
    }

    // Add citations
    lines.push('Sources:');
    for (const result of results) {
      lines.push(`${result.citationId} ${result.title} - ${result.displayUrl}`);
    }

    return lines.join('\n');
  }

  /**
   * Create display content with rich formatting
   */
  createDisplayContent(query, results, context) {
    const lines = [];
    
    // Header
    lines.push('🔍 Web Search Results');
    lines.push(`📝 Query: "${query}"`);
    lines.push(`📊 Found ${results.length} results`);
    lines.push('');

    if (results.length === 0) {
      lines.push('❌ No search results found.');
      return lines.join('\n');
    }

    // Results
    for (const result of results) {
      lines.push(`${result.rank}. 📄 ${result.title}`);
      lines.push(`   🔗 ${result.url}`);
      lines.push(`   📅 ${result.publishedDate ? new Date(result.publishedDate).toLocaleDateString() : 'Unknown date'}`);
      lines.push(`   📝 ${result.snippet}`);
      lines.push('');
    }

    // Search options summary
    lines.push('⚙️  Search Options:');
    lines.push(`   Results: ${context.options.numResults}`);
    lines.push(`   Safe search: ${context.options.safeSearch}`);
    lines.push(`   Country: ${context.options.country}`);
    lines.push(`   Language: ${context.options.language}`);
    
    if (context.options.timeRange !== 'any') {
      lines.push(`   Time range: ${context.options.timeRange}`);
    }

    lines.push('');
    
    // Citations
    lines.push('📚 Sources:');
    for (const result of results) {
      lines.push(`   ${result.citationId} ${result.title} - ${result.displayUrl}`);
    }

    return lines.join('\n');
  }

  /**
   * Insert citation markers in text
   */
  insertCitationMarkers(text, citations) {
    // This would be used to insert citation markers in AI-generated responses
    // based on the search results
    
    let markedText = text;
    
    for (const citation of citations) {
      // Simple implementation - in practice, this would be more sophisticated
      const keywords = citation.title.split(' ').slice(0, 3);
      
      for (const keyword of keywords) {
        if (keyword.length > 3) {
          const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
          markedText = markedText.replace(regex, `${keyword}${citation.citationId}`);
        }
      }
    }
    
    return markedText;
  }
}

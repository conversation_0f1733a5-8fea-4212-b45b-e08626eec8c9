/**
 * Tool system type definitions and constants
 */

// Tool execution states
export const ToolState = {
  PENDING: 'pending',
  VALIDATING: 'validating',
  CONFIRMING: 'confirming',
  EXECUTING: 'executing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

// Tool categories
export const ToolCategory = {
  FILESYSTEM: 'filesystem',
  EXECUTION: 'execution',
  WEB: 'web',
  MEMORY: 'memory',
  MCP: 'mcp',
  UTILITY: 'utility'
};

// Tool permission levels
export const PermissionLevel = {
  SAFE: 'safe',           // No confirmation needed
  MODERATE: 'moderate',   // Confirmation recommended
  DANGEROUS: 'dangerous'  // Always require confirmation
};

// Tool result types
export const ResultType = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// File processing types
export const FileType = {
  TEXT: 'text',
  IMAGE: 'image',
  PDF: 'pdf',
  BINARY: 'binary',
  DIRECTORY: 'directory'
};

// Supported image formats
export const SupportedImageFormats = [
  '.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp'
];

// Default exclusion patterns for file operations
export const DefaultExclusions = [
  'node_modules/**',
  '.git/**',
  '.svn/**',
  '.hg/**',
  'dist/**',
  'build/**',
  'coverage/**',
  '.nyc_output/**',
  'tmp/**',
  'temp/**',
  '*.log',
  '.DS_Store',
  'Thumbs.db'
];

// Shell command whitelist
export const SafeCommands = [
  'ls', 'dir', 'pwd', 'echo', 'cat', 'head', 'tail', 'grep', 'find',
  'wc', 'sort', 'uniq', 'cut', 'awk', 'sed', 'date', 'whoami',
  'git', 'npm', 'node', 'python', 'pip', 'cargo', 'go', 'java',
  'mvn', 'gradle', 'make', 'cmake', 'docker', 'kubectl'
];

// Tool configuration defaults
export const ToolDefaults = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 100,
  timeout: 30000, // 30 seconds
  maxOutputLength: 50000, // 50KB
  confirmationTimeout: 60000 // 1 minute
};

// MCP Transport types
export const MCPTransportType = {
  STDIO: 'stdio',
  SSE: 'sse',
  HTTP: 'http'
};

// MCP Server configuration
export const MCPDefaults = {
  connectionTimeout: 10000, // 10 seconds
  requestTimeout: 30000, // 30 seconds
  maxRetries: 3,
  retryDelay: 1000 // 1 second
};

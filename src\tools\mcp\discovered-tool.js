import { BaseTool } from '../base/base-tool.js';
import { ToolResult } from '../base/tool-result.js';
import { ToolCategory, PermissionLevel } from '../base/tool-types.js';
import processManager from '../utils/process-manager.js';
import logger from '../../utils/logger.js';

/**
 * DiscoveredTool - Executes custom tools discovered via shell commands
 */
export class DiscoveredTool extends BaseTool {
  constructor(toolDefinition, discoveryConfig) {
    super({
      name: toolDefinition.name,
      description: toolDefinition.description || 'Dynamically discovered tool',
      category: ToolCategory.MCP,
      permissionLevel: PermissionLevel.DANGEROUS
    });
    
    this.toolDefinition = toolDefinition;
    this.discoveryConfig = discoveryConfig;
    this.parameters = toolDefinition.parameters || {};
  }

  /**
   * Get function definition for LLM
   */
  getFunctionDefinition() {
    return {
      name: this.name,
      description: this.description,
      parameters: this.parameters
    };
  }

  /**
   * Validate tool parameters
   */
  async validateParams(params) {
    const errors = [];

    // Validate against tool definition parameters
    if (this.parameters.required) {
      for (const requiredParam of this.parameters.required) {
        if (!(requiredParam in params)) {
          errors.push(`Missing required parameter: ${requiredParam}`);
        }
      }
    }

    // Validate parameter types if defined
    if (this.parameters.properties) {
      for (const [paramName, paramValue] of Object.entries(params)) {
        const paramDef = this.parameters.properties[paramName];
        if (paramDef && paramDef.type) {
          const actualType = typeof paramValue;
          const expectedType = paramDef.type;
          
          if (expectedType === 'integer' && !Number.isInteger(paramValue)) {
            errors.push(`Parameter ${paramName} must be an integer`);
          } else if (expectedType === 'number' && typeof paramValue !== 'number') {
            errors.push(`Parameter ${paramName} must be a number`);
          } else if (expectedType === 'string' && typeof paramValue !== 'string') {
            errors.push(`Parameter ${paramName} must be a string`);
          } else if (expectedType === 'boolean' && typeof paramValue !== 'boolean') {
            errors.push(`Parameter ${paramName} must be a boolean`);
          } else if (expectedType === 'array' && !Array.isArray(paramValue)) {
            errors.push(`Parameter ${paramName} must be an array`);
          } else if (expectedType === 'object' && (typeof paramValue !== 'object' || Array.isArray(paramValue))) {
            errors.push(`Parameter ${paramName} must be an object`);
          }
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if operation is risky
   */
  isRiskyOperation(params) {
    // All discovered tools are considered risky by default
    return true;
  }

  /**
   * Get confirmation message
   */
  getConfirmationMessage(params) {
    return `Execute discovered tool "${this.name}"?\n\nDescription: ${this.description}\n\nParameters:\n${JSON.stringify(params, null, 2)}\n\nDiscovery Command: ${this.discoveryConfig.toolCallCommand}`;
  }

  /**
   * Execute the discovered tool
   */
  async execute(params) {
    try {
      logger.debug(`Executing discovered tool: ${this.name}`);
      
      // Prepare the tool call command
      const toolCallCommand = this.discoveryConfig.toolCallCommand.replace('<toolName>', this.name);
      
      logger.debug(`Tool call command: ${toolCallCommand}`);

      // Execute the tool command
      const result = await processManager.executeCommand(toolCallCommand, {
        cwd: this.discoveryConfig.workingDirectory || process.cwd(),
        timeout: this.discoveryConfig.timeout || 30000
      });

      // Send parameters as JSON to stdin if the process is still running
      if (result.processId && Object.keys(params).length > 0) {
        try {
          // For tools that expect JSON input on stdin
          const jsonInput = JSON.stringify(params);
          logger.debug(`Sending parameters to tool: ${jsonInput}`);
          
          // Note: This is a simplified approach. In a real implementation,
          // you might need to handle stdin/stdout communication differently
          // depending on the tool's interface
        } catch (error) {
          logger.warn('Failed to send parameters to tool:', error.message);
        }
      }

      // Process the result
      if (result.exitCode === 0) {
        const content = this.formatToolOutput(result.stdout, result.stderr);
        
        return ToolResult.success(content, {
          displayContent: content,
          metadata: {
            toolName: this.name,
            exitCode: result.exitCode,
            executionTime: result.executionTime,
            command: toolCallCommand,
            parameters: params
          }
        });
      } else {
        const errorMessage = `Tool execution failed with exit code ${result.exitCode}`;
        const errorDetails = result.stderr || result.stdout || 'No error details available';
        
        return ToolResult.error(`${errorMessage}\n\nDetails: ${errorDetails}`, {
          metadata: {
            toolName: this.name,
            exitCode: result.exitCode,
            executionTime: result.executionTime,
            command: toolCallCommand,
            parameters: params,
            stdout: result.stdout,
            stderr: result.stderr
          }
        });
      }

    } catch (error) {
      logger.error(`Discovered tool execution failed for ${this.name}:`, error.message);
      return ToolResult.error(error, {
        metadata: {
          toolName: this.name,
          parameters: params,
          discoveryConfig: this.discoveryConfig
        }
      });
    }
  }

  /**
   * Format tool output for display
   */
  formatToolOutput(stdout, stderr) {
    let content = '';
    
    if (stdout && stdout.trim()) {
      content += `Output:\n${stdout.trim()}`;
    }
    
    if (stderr && stderr.trim()) {
      if (content) content += '\n\n';
      content += `Errors/Warnings:\n${stderr.trim()}`;
    }
    
    if (!content) {
      content = 'Tool executed successfully with no output.';
    }
    
    return content;
  }

  /**
   * Get tool information
   */
  getInfo() {
    return {
      name: this.name,
      description: this.description,
      category: this.category,
      permissionLevel: this.permissionLevel,
      parameters: this.parameters,
      discoveryConfig: this.discoveryConfig,
      isDynamic: true
    };
  }
}

/**
 * Tool Discovery Manager - Discovers and registers dynamic tools
 */
export class ToolDiscoveryManager {
  constructor() {
    this.discoveredTools = new Map();
    this.discoveryConfigs = new Map();
  }

  /**
   * Add a discovery configuration
   */
  addDiscoveryConfig(name, config) {
    const requiredFields = ['toolDiscoveryCommand', 'toolCallCommand'];
    for (const field of requiredFields) {
      if (!config[field]) {
        throw new Error(`Discovery config missing required field: ${field}`);
      }
    }

    this.discoveryConfigs.set(name, {
      name,
      toolDiscoveryCommand: config.toolDiscoveryCommand,
      toolCallCommand: config.toolCallCommand,
      workingDirectory: config.workingDirectory || process.cwd(),
      timeout: config.timeout || 30000,
      enabled: config.enabled !== false
    });

    logger.debug(`Added discovery config: ${name}`);
  }

  /**
   * Discover tools using configured discovery commands
   */
  async discoverTools(configName = null) {
    const configs = configName 
      ? [this.discoveryConfigs.get(configName)]
      : Array.from(this.discoveryConfigs.values());

    const discoveredTools = [];

    for (const config of configs) {
      if (!config || !config.enabled) continue;

      try {
        logger.debug(`Discovering tools with config: ${config.name}`);
        
        const result = await processManager.executeCommand(config.toolDiscoveryCommand, {
          cwd: config.workingDirectory,
          timeout: config.timeout
        });

        if (result.exitCode === 0 && result.stdout) {
          const tools = this.parseToolDefinitions(result.stdout);
          
          for (const toolDef of tools) {
            const discoveredTool = new DiscoveredTool(toolDef, config);
            this.discoveredTools.set(toolDef.name, discoveredTool);
            discoveredTools.push(discoveredTool);
            
            logger.debug(`Discovered tool: ${toolDef.name}`);
          }
        } else {
          logger.warn(`Tool discovery failed for config ${config.name}: ${result.stderr}`);
        }

      } catch (error) {
        logger.error(`Tool discovery error for config ${config.name}:`, error.message);
      }
    }

    return discoveredTools;
  }

  /**
   * Parse tool definitions from discovery command output
   */
  parseToolDefinitions(output) {
    try {
      // Expect JSON array of tool definitions
      const parsed = JSON.parse(output);
      
      if (!Array.isArray(parsed)) {
        throw new Error('Tool definitions must be a JSON array');
      }

      return parsed.filter(tool => tool.name && typeof tool.name === 'string');
      
    } catch (error) {
      logger.error('Failed to parse tool definitions:', error.message);
      return [];
    }
  }

  /**
   * Get discovered tool by name
   */
  getTool(name) {
    return this.discoveredTools.get(name);
  }

  /**
   * Get all discovered tools
   */
  getAllTools() {
    return Array.from(this.discoveredTools.values());
  }

  /**
   * Clear discovered tools
   */
  clearTools() {
    this.discoveredTools.clear();
  }
}

export default new ToolDiscoveryManager();

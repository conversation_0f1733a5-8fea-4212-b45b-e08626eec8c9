/**
 * Tool system configuration and setup utilities
 */

import { configureToolDiscovery, configureMCPServers, addToMCPAllowlist } from '../index.js';
import logger from '../../utils/logger.js';

/**
 * Default tool discovery configurations
 */
export const defaultDiscoveryConfigs = {
  // Example: Custom CLI tools discovery
  'custom-cli-tools': {
    toolDiscoveryCommand: 'custom-tools list --format=json',
    toolCallCommand: 'custom-tools call <toolName>',
    workingDirectory: process.cwd(),
    timeout: 30000,
    enabled: false // Disabled by default
  },

  // Example: Python script tools
  'python-tools': {
    toolDiscoveryCommand: 'python tools/discover.py',
    toolCallCommand: 'python tools/execute.py <toolName>',
    workingDirectory: process.cwd(),
    timeout: 30000,
    enabled: false // Disabled by default
  }
};

/**
 * Default MCP server configurations
 */
export const defaultMCPServers = {
  // Example: Local stdio MCP server
  'local-filesystem': {
    type: 'stdio',
    command: 'mcp-server-filesystem',
    args: ['--root', process.cwd()],
    workingDirectory: process.cwd(),
    enabled: false // Disabled by default
  },

  // Example: HTTP MCP server
  'remote-api': {
    type: 'http',
    url: 'http://localhost:8080',
    headers: {
      'Authorization': 'Bearer your-token-here'
    },
    enabled: false // Disabled by default
  },

  // Example: SSE MCP server
  'sse-server': {
    type: 'sse',
    url: 'http://localhost:8081',
    enabled: false // Disabled by default
  }
};

/**
 * Load tool configuration from environment or config file
 */
export function loadToolConfig() {
  const config = {
    discovery: { ...defaultDiscoveryConfigs },
    mcpServers: { ...defaultMCPServers },
    mcpAllowlist: [],
    webSearch: {
      enableDuckDuckGo: true,
      enableSerpAPI: !!process.env.SERPAPI_KEY,
      enableBing: !!process.env.BING_SEARCH_API_KEY
    }
  };

  // Override with environment variables
  if (process.env.TOOL_DISCOVERY_ENABLED === 'true') {
    config.discovery['custom-cli-tools'].enabled = true;
  }

  if (process.env.MCP_FILESYSTEM_ENABLED === 'true') {
    config.mcpServers['local-filesystem'].enabled = true;
  }

  // Load MCP allowlist from environment
  if (process.env.MCP_ALLOWLIST) {
    config.mcpAllowlist = process.env.MCP_ALLOWLIST.split(',').map(s => s.trim());
  }

  return config;
}

/**
 * Apply tool configuration
 */
export function applyToolConfig(config = null) {
  if (!config) {
    config = loadToolConfig();
  }

  try {
    // Configure tool discovery
    if (config.discovery) {
      configureToolDiscovery(config.discovery);
      logger.debug('Tool discovery configured');
    }

    // Configure MCP servers
    if (config.mcpServers) {
      configureMCPServers(config.mcpServers);
      logger.debug('MCP servers configured');
    }

    // Apply MCP allowlist
    if (config.mcpAllowlist && config.mcpAllowlist.length > 0) {
      for (const entry of config.mcpAllowlist) {
        if (entry.includes(':')) {
          const [serverName, toolName] = entry.split(':', 2);
          addToMCPAllowlist(serverName, toolName);
        } else {
          addToMCPAllowlist(entry);
        }
      }
      logger.debug(`Applied MCP allowlist: ${config.mcpAllowlist.join(', ')}`);
    }

    logger.info('Tool configuration applied successfully');
    return true;

  } catch (error) {
    logger.error('Failed to apply tool configuration:', error.message);
    return false;
  }
}

/**
 * Validate tool configuration
 */
export function validateToolConfig(config) {
  const errors = [];

  // Validate discovery configs
  if (config.discovery) {
    for (const [name, discoveryConfig] of Object.entries(config.discovery)) {
      if (!discoveryConfig.toolDiscoveryCommand) {
        errors.push(`Discovery config '${name}' missing toolDiscoveryCommand`);
      }
      if (!discoveryConfig.toolCallCommand) {
        errors.push(`Discovery config '${name}' missing toolCallCommand`);
      }
    }
  }

  // Validate MCP server configs
  if (config.mcpServers) {
    for (const [name, serverConfig] of Object.entries(config.mcpServers)) {
      if (!serverConfig.type) {
        errors.push(`MCP server '${name}' missing type`);
      }
      
      if (serverConfig.type === 'stdio' && !serverConfig.command) {
        errors.push(`Stdio MCP server '${name}' missing command`);
      }
      
      if ((serverConfig.type === 'http' || serverConfig.type === 'sse') && !serverConfig.url) {
        errors.push(`${serverConfig.type.toUpperCase()} MCP server '${name}' missing url`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get tool configuration status
 */
export function getToolConfigStatus() {
  return {
    webSearch: {
      duckDuckGo: true, // Always available
      serpAPI: !!process.env.SERPAPI_KEY,
      bing: !!process.env.BING_SEARCH_API_KEY
    },
    environment: {
      hasToolDiscoveryEnabled: process.env.TOOL_DISCOVERY_ENABLED === 'true',
      hasMCPFilesystemEnabled: process.env.MCP_FILESYSTEM_ENABLED === 'true',
      hasMCPAllowlist: !!process.env.MCP_ALLOWLIST,
      serpAPIKey: !!process.env.SERPAPI_KEY,
      bingAPIKey: !!process.env.BING_SEARCH_API_KEY
    }
  };
}

/**
 * Generate example configuration file
 */
export function generateExampleConfig() {
  return {
    // Tool discovery configurations
    discovery: {
      'my-custom-tools': {
        toolDiscoveryCommand: 'my-tool-manager list --json',
        toolCallCommand: 'my-tool-manager execute <toolName>',
        workingDirectory: '/path/to/tools',
        timeout: 30000,
        enabled: true
      }
    },

    // MCP server configurations
    mcpServers: {
      'filesystem-server': {
        type: 'stdio',
        command: 'mcp-server-filesystem',
        args: ['--root', '/path/to/files'],
        enabled: true
      },
      'api-server': {
        type: 'http',
        url: 'https://api.example.com/mcp',
        headers: {
          'Authorization': 'Bearer your-api-token'
        },
        enabled: true
      }
    },

    // MCP allowlist (server:tool or just server)
    mcpAllowlist: [
      'filesystem-server',           // Allow all tools from this server
      'api-server:safe-tool',        // Allow only specific tool
      'trusted-server:another-tool'
    ],

    // Web search configuration
    webSearch: {
      enableDuckDuckGo: true,
      enableSerpAPI: true,  // Requires SERPAPI_KEY env var
      enableBing: true      // Requires BING_SEARCH_API_KEY env var
    }
  };
}

/**
 * Setup wizard for tool configuration
 */
export async function setupWizard() {
  logger.info('Starting tool configuration setup wizard...');
  
  const config = loadToolConfig();
  const status = getToolConfigStatus();
  
  logger.info('Current configuration status:');
  logger.info(`- Web Search APIs: DuckDuckGo (✓), SerpAPI (${status.webSearch.serpAPI ? '✓' : '✗'}), Bing (${status.webSearch.bing ? '✓' : '✗'})`);
  logger.info(`- Tool Discovery: ${status.environment.hasToolDiscoveryEnabled ? 'Enabled' : 'Disabled'}`);
  logger.info(`- MCP Filesystem: ${status.environment.hasMCPFilesystemEnabled ? 'Enabled' : 'Disabled'}`);
  logger.info(`- MCP Allowlist: ${status.environment.hasMCPAllowlist ? 'Configured' : 'Not configured'}`);
  
  // Validation
  const validation = validateToolConfig(config);
  if (!validation.isValid) {
    logger.warn('Configuration validation errors:');
    validation.errors.forEach(error => logger.warn(`  - ${error}`));
  }
  
  // Apply configuration
  const applied = applyToolConfig(config);
  if (applied) {
    logger.info('Tool configuration setup completed successfully!');
  } else {
    logger.error('Tool configuration setup failed. Check the logs for details.');
  }
  
  return applied;
}

export default {
  loadToolConfig,
  applyToolConfig,
  validateToolConfig,
  getToolConfigStatus,
  generateExampleConfig,
  setupWizard,
  defaultDiscoveryConfigs,
  defaultMCPServers
};

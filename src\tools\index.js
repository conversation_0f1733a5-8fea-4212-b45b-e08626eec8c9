/**
 * Tool system exports and initialization
 */

// Base classes
export { BaseTool } from './base/base-tool.js';
export { ToolResult } from './base/tool-result.js';
export { ToolRegistry } from './base/tool-registry.js';
export * from './base/tool-types.js';

// Utilities
export { default as fileService } from './utils/file-service.js';
export { default as contentProcessor } from './utils/content-processor.js';
export { default as diffGenerator } from './utils/diff-generator.js';
export { default as processManager } from './utils/process-manager.js';

// File system tools
export { LSTool } from './filesystem/ls-tool.js';
export { ReadFileTool } from './filesystem/read-file-tool.js';
export { WriteFileTool } from './filesystem/write-file-tool.js';
export { EditTool } from './filesystem/edit-tool.js';
export { GlobTool } from './filesystem/glob-tool.js';
export { GrepTool } from './filesystem/grep-tool.js';
export { ReadManyFilesTool } from './filesystem/read-many-files-tool.js';

// Execution tools
export { ShellTool } from './execution/shell-tool.js';

// Web tools
export { WebFetchTool } from './web/web-fetch-tool.js';
export { WebSearchTool } from './web/web-search-tool.js';

// Memory tools
export { MemoryTool } from './memory/memory-tool.js';

// Registry instance
import toolRegistry from './base/tool-registry.js';

// Tool instances
import { LSTool } from './filesystem/ls-tool.js';
import { ReadFileTool } from './filesystem/read-file-tool.js';
import { WriteFileTool } from './filesystem/write-file-tool.js';
import { EditTool } from './filesystem/edit-tool.js';
import { GlobTool } from './filesystem/glob-tool.js';
import { GrepTool } from './filesystem/grep-tool.js';
import { ReadManyFilesTool } from './filesystem/read-many-files-tool.js';
import { ShellTool } from './execution/shell-tool.js';
import { WebFetchTool } from './web/web-fetch-tool.js';
import { WebSearchTool } from './web/web-search-tool.js';
import { MemoryTool } from './memory/memory-tool.js';

// MCP managers
import toolDiscoveryManager from './mcp/discovered-tool.js';
import mcpServerManager from './mcp/discovered-mcp-tool.js';

/**
 * Initialize and register all tools
 */
export async function initializeTools() {
  // File system tools
  toolRegistry.register(new LSTool());
  toolRegistry.register(new ReadFileTool());
  toolRegistry.register(new WriteFileTool());
  toolRegistry.register(new EditTool());
  toolRegistry.register(new GlobTool());
  toolRegistry.register(new GrepTool());
  toolRegistry.register(new ReadManyFilesTool());

  // Execution tools
  toolRegistry.register(new ShellTool());

  // Web tools
  toolRegistry.register(new WebFetchTool());
  toolRegistry.register(new WebSearchTool());

  // Memory tools
  toolRegistry.register(new MemoryTool());

  // Mark as initialized
  await toolRegistry.initialize();

  // Discover and register dynamic tools
  await toolRegistry.discoverDynamicTools();
}

/**
 * Configure tool discovery
 */
export function configureToolDiscovery(configs) {
  for (const [name, config] of Object.entries(configs)) {
    toolDiscoveryManager.addDiscoveryConfig(name, config);
  }
}

/**
 * Configure MCP servers
 */
export function configureMCPServers(servers) {
  for (const [name, config] of Object.entries(servers)) {
    mcpServerManager.addServer(name, config);
  }
}

/**
 * Add to MCP allowlist
 */
export function addToMCPAllowlist(serverName, toolName = null) {
  mcpServerManager.addToAllowlist(serverName, toolName);
}

/**
 * Get the tool registry instance
 */
export function getToolRegistry() {
  return toolRegistry;
}

/**
 * Execute a tool by name
 */
export async function executeTool(toolName, params) {
  return await toolRegistry.executeTool(toolName, params);
}

/**
 * Get function definitions for all tools
 */
export function getToolFunctionDefinitions() {
  return toolRegistry.getFunctionDefinitions();
}

/**
 * Get function definitions by category
 */
export function getToolFunctionDefinitionsByCategory(category) {
  return toolRegistry.getFunctionDefinitionsByCategory(category);
}

/**
 * Get tool statistics
 */
export function getToolStats() {
  return toolRegistry.getStats();
}

// Export the registry instance as default
export default toolRegistry;

// Export MCP managers
export { toolDiscoveryManager, mcpServerManager };

// Export MCP classes for advanced usage
export { DiscoveredTool, ToolDiscoveryManager } from './mcp/discovered-tool.js';
export { DiscoveredMCPTool, MCPServerManager } from './mcp/discovered-mcp-tool.js';
export { MCPTransportFactory, StdioMCPTransport, SSEMCPTransport, HTTPMCPTransport } from './mcp/mcp-transport.js';
